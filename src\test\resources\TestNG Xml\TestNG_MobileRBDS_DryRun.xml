<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE suite SYSTEM "https://testng.org/testng-1.0.dtd">
<suite name="Run Cucumber TestNG" parallel="tests"
	data-provider-thread-count="4" thread-count="1" >

	<listeners>
		<listener class-name="com.rai.framework.TestNGListener" />
	</listeners>
	
	<test name="Test for RBDS - Dry Run">
		<parameter name="ExecutionMode" value="SAUCEMOBILE" />
		<parameter name="ToolName" value="APPIUM" />
		<parameter name="MobileExecutionPlatform" value="WEB_ANDROID"/>
		<parameter name="BrowserName" value="CHROME" />
		<parameter name="DeviceName" value=""/>
		<classes>

            <class name= "com.rai.runners.RunnerDryRun"/>

		</classes>
	</test>	
	
</suite>
