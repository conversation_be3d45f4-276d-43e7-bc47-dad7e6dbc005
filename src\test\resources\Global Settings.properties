x	# Framework settings file

ProjectName=Sample Application

#Default Settings - To Avoid Null Pointer within the Framework
DateFormatString=dd-MMM-yyyy hh:mm:ss a

#Capture Every Execution Report in Seperate Folder
SaveReports=True

# User-defined Settings
#ApplicationUrl=http://newtours.demoaut.com/

# Remote Execution & Grid Settings (Applicable only if the ExecutionMode is "REMOTE")
#RemoteUrl=http://************:4444/wd/hub

#Appium Settings
AppiumURL=http://127.0.0.1:4723/wd/hub
#AppiumURL=http://rai.perfectomobile.com/nexperience/perfectomobile/wd/hub
# For Appium Parallel Execution use below URL
#AppiumURL=http://*************:4444/wd/hub

# SauceLabs Settings
SauceLabsHost=https://ondemand.us-west-1.saucelabs.com:443/wd/hub
SauceUsername=ravimidathala
SauceAccessKey=3a37c0800eee4623a7b2771cdab7ad06
SauceLabMobileHost=https://Chandri:<EMAIL>:443/wd/hub
#PerfectoHostDefault=rai.perfectomobile.com
#PerfectoUser=Chandri*1
#PerfectoPassword=Cgi#ujx4pi
#PerfectoAndroidIdentifier=NP$
#PerfecttoIosBundleID=
#PerfectoReportGeneration=False
#SecurityToken=eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJnclN5Wmc4T2QwQWhnTjVIeUFsd042Xzl1Q1BxdFFjQW9fNnowaElaQm40In0.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.M9UP37MGFsCQtnDOY0cecCguJTj7toUm661yyynbFaV6hPqghYnovKTmVQh8MxeelkJxUx3qDW24ar5H9pdtstb6mlgekb4CTWOVAaZ83nDhXGBRgTAnOTj-XmWFerki9BjP9USxesFcbbA4a7jDNtvQ2sLsei1rt9du8JE8NAqEv3KjUeCHXYe7X5tghRhSt6XDy0_qvrVfvrpEaiGaxC1M25CSESQTbqIVdxPciteav6AJbPW5l5YzPl05A99xfP0DrcTnHD8Z6vKK46I9SVh1l9vZ9IwIFgUOhISyIkZx3fixvHeNeOAgMH-l-vSYw5f4bRA8NwBfHlSCh4oARQ
#SecurityToken=eyJhbGciOiJIUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICIzZWM3OTBlOS0yNzEzLTRkNWUtOWVlYy1hMjJiMzM1NjFmYWQifQ.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.8n4ulgDL8ZiLstaetkjEwp_ur_ZQ2mRWirvgL7G1iRA
SecurityToken=********-12da-48d9-8dee-b1147244d7a4
Resolution=1366x768
Location=US East

# Android Device and Application Details
#Application_Package_Name=com.rjrt.NewPort
#Application_MainActivity_Name=com.rjrt.NewPort.NewPort
#Application_Package_Name=com.rjrt.TheOasis
#Application_MainActivity_Name=com.rjrt.TheOasis.CamelCoupons
Application_Package_Name=com.rjrt.Grizzly
Application_MainActivity_Name=com.rjrt.Grizzly.Grizzly
InstallApplicationInDevice=True
#AndroidApplicationPath=PUBLIC:NP_PersonalizedCoupon_AltHome_v3.5.7_ScreenshotEnabled.apk
AndroidApplicationPath=PUBLIC:Grizzly_AltHome_QA_v3.5.4_ScreenshotEnabled.apk
#AndroidApplicationPath=PUBLIC:Camel_AltHome_QA_v3.5.2_ScreenshotEnabled.apk
iPhoneApplicationPath=C:\\SampleApps\\TestMunk.ipa
iPhoneBundleID=com.mcoe.TestmunkDemo
ResetApp=True

#Default Settings
DefaultToolName = APPIUM
DefaultDeviceName=
DefaultMobileExecutionPlatform= ANDROID
DefautMobileOsVersion=
DefaultBrowser=CHROME
DefaultBrowserVersion=50
DefaultPlatform=Android
DefaultPlatformVersion=12
DefaultManufacturer=Google
DefaultModel=pixel 4a 5g

#
##Default Settings
#DefaultToolName = APPIUM
#DefaultDeviceName=
#DefaultMobileExecutionPlatform=WEB_IOS
#DefautMobileOsVersion=
#DefaultBrowser=SAFARI
#DefaultBrowserVersion=50
#DefaultPlatform=IOS
#DefaultPlatformVersion=17.3
#DefaultManufacturer=APPLE
#DefaultModel=iPhone 14 Pro
#
#









# OpenCV DLL files path
OpenCvDllFilePath= /src/test/resources/OpenCV/opencv_java342.dll

# SGW Images path
SGWImagesPath=./src/test/resources/SGW_Images/

#Proxy Details:
#ProxyHost =gateway.zscaler.net
#ProxyPort=80
#ProxyUsername=midathr1
#ProxyPassword=may-2020

# Screenshot Control
TakeScreenshotForPassedStep=false
TakeScreenshotForFailedStep=true

ProxyHost =
ProxyPort=
ProxyUsername=
ProxyPassword=


# Database Details
#ServerName=P2_QA_RAI_MKTG.raiconmktg.local
ServerName=************
DatabaseName=RAI_APP
DBUsername=svc-qa-drupal-r
DBPassword=Test!232018
