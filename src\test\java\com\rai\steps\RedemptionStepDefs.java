package com.rai.steps;

import static org.testng.Assert.fail;

import java.util.*;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.interactions.Actions;
import org.testng.Assert;
import com.rai.framework.DriverManager;
import com.rai.pages.ContentPageObjects;
import com.rai.pages.CouponHomePageObjects;
import com.rai.pages.LocationServices;
import com.rai.pages.RedeemNowPageObjects;
import com.rai.pages.StoreListViewPageObjects;
import io.appium.java_client.AppiumDriver;
import io.appium.java_client.android.AndroidDriver;
import io.appium.java_client.android.nativekey.AndroidKey;
import io.appium.java_client.android.nativekey.KeyEvent;
import io.cucumber.java.en.And;
import io.cucumber.java.en.Then;
import io.cucumber.java.en.When;

public class RedemptionStepDefs extends MasterSteps {

	static Logger log = LogManager.getLogger(RedemptionStepDefs.class);
	AppiumDriver driver = DriverManager.getAppiumDriver();

	@Then("I validate the timer running")
		public void i_validate_the_timer_running() throws Throwable {
			try {
				waitUntilPageReadyStateComplete(15);
				//waitUntilElementVisible(StoreListViewPageObjects.txt_Timer, 100);
				WebElement element = driver.findElement(StoreListViewPageObjects.txt_Timer);
				Actions action = new Actions(driver);
				action.moveToElement(element).build().perform();
				String[] temp = element.getText().split(":");
				int oldTimer = Integer.parseInt(temp[0]);
				System.out.println(oldTimer);
				Thread.sleep(30000);
				if (element.isDisplayed()) {
					highLightElement(element, "LawnGreen");
					waitFor(9000);
					Thread.sleep(30000);
					temp = element.getText().split(":");
					int newTimer = Integer.parseInt(temp[0]);
					System.out.println(newTimer);
					if (oldTimer == 4 && newTimer < 4) {
						//DriverManager.getReportiumClient().reportiumAssert("5 minutes timer is present and running", true);
						//DriverManager.getReportiumClient().stepEnd();
						Assert.assertTrue(true, "5 minutes timer is present and running");
						addStepLog("5 minutes timer is present and running");
						attachScreenshotForMobile(true);
					} else {
						//DriverManager.getReportiumClient().reportiumAssert("5 minutes timer is present but NOT running",
//								false);
						//DriverManager.getReportiumClient().stepEnd();
						Assert.assertTrue(false, "5 minutes timer is present but NOT running");
						addStepLog("5 minutes timer is present but not running");
						attachScreenshotForMobile(false);
					}
				} else {
					//DriverManager.getReportiumClient().reportiumAssert("5 minutes timer is not found", false);
					//DriverManager.getReportiumClient().stepEnd();
					Assert.assertTrue(false, "5 minutes timer is not found");
					attachScreenshotForMobile(false);
					fail("5 minutes timer is not found");
				}
			} catch (Exception e) {
				failTestScript("Failed to validate the running timer: ",e);
			}
	 
		}
	
	@And("^I click on the I'm Done button")
	public void i_click_on_im_done_button() throws Throwable {
		try {
			Thread.sleep(10000);
			if(LoginPageStepDefs.getBrand().equalsIgnoreCase("NEWPORT")) {
				waitUntilElementVisible(StoreListViewPageObjects.btn_ImDone, 20);
				Thread.sleep(2000);	
				clickIfElementPresent(StoreListViewPageObjects.btn_ImDone, "Redemption Page - I'm Done button");
				waitUntilElementVisible(StoreListViewPageObjects.btn_ImDone_newport, 30);
				clickIfElementPresent(StoreListViewPageObjects.btn_ImDone_newport, "Survey_Done button");

			}else {
			//DriverManager.getReportiumClient().stepStart("Step:The user will click on I am done Button");
		//	waitUntilElementVisible(StoreListViewPageObjects.btn_ImDone, 20);
			clickIfElementPresent(StoreListViewPageObjects.btn_ImDone, "Redemption Page - I'm Done button");
			clickIfElementPresent(RedeemNowPageObjects.confirmationPopupYes, "Yes - Confirmation Popup");  
	//		clickIfElementPresent(StoreListViewPageObjects.btn_NotNowAEM, "Yes - Confirmation Popup");
			}
			//DriverManager.getReportiumClient().stepEnd();
		} catch (Exception e) {
			System.out.println("Exception while clicking on I'm done button: " + e.getMessage());
			failTestScript("Failed to click on I'm done button: ",e);
		}
	}

	@And("^I Validate the Grizzly Content Page$")
	public void i_validate_the_Grizzly_content_page() throws Throwable {
		//DriverManager.getReportiumClient()
//				.stepStart("Step:The user will validate the content page after coupon redemption");
		try {
			if (isElementPresent(CouponHomePageObjects.btngreentick)) {
				String success_message = "You Have More Offers!";
				isElementPresentContainsText(CouponHomePageObjects.offermessage, success_message,
						"More offers message");
				isElementPresentVerification(CouponHomePageObjects.lnkbacktocoupon, "Back to Coupon Link");
			} else {
				String Nocoupon_message = "YOU'VE REDEEMED ALL COUPONS, CHECK BACK FOR MORE IN THE FUTURE";
				isElementPresentContainsText(CouponHomePageObjects.grizzly_NoCouponsMsg, Nocoupon_message,
						"No Coupon available message");
			}
			clickIfElementPresent(CouponHomePageObjects.grizzly_contentStartQuizz, "start Quizz link");
			waitUntilElementVisible(CouponHomePageObjects.grizzly_btnStartQuizz, 40);
			isElementPresentVerification(CouponHomePageObjects.grizzly_btnStartQuizz, "Start Quizz button");
			((AndroidDriver) driver).pressKey(new KeyEvent().withKey(AndroidKey.BACK));
			//DriverManager.getReportiumClient().stepEnd();
		} catch (Exception e) {
			failTestScript("Failed to validate the Grizzly content page: ",e);
		}
	}

	@And("^I Validate the Content Page$")
	public void i_validate_the_content_page() throws Throwable {
		//DriverManager.getReportiumClient().stepStart("Step:The user will validate the content page after coupon redemption");
		try {
			Thread.sleep(5000);
			waitUntilPageReadyStateComplete(20);
			//waitUntilElementVisible(ContentPageObjects.contentScreenFirstNme, 30);
			/*if (isElementPresent(ContentPageObjects.imageTick)) {
				String success_message=null;
				if(brand.get().contains("Cougar")) {
					success_message = "YOU HAVE MORE OFFERS!";
				}else {
					success_message = "You Have More Offers!";
				}
				System.out.println(success_message);
								
				isElementPresentVerification(ContentPageObjects.imageTick, "Content Screen - Image- Tick");
				isElementPresentVerification(ContentPageObjects.contentScreenFirstNme, "Content Screen - First Name");
						
				isElementPresentContainsText(ContentPageObjects.offeravailabilitymessage, success_message,"YOU HAVE MORE OFFERS!");
				isElementPresentVerification(ContentPageObjects.lnkbacktocoupon, "Back to Coupon Link");
				isElementPresentVerification(ContentPageObjects.imgTile1, "Content Screen - Tile 1");
				isElementPresentVerification(ContentPageObjects.imgTile2, "Content Screen - Tile 2");
				//isElementPresentVerification(ContentPageObjects.warninginfo, "Warning Info - Social Media");
			}else{
				String Nocoupon_message =null;
				if(brand.get().contains("Cougar")) {
					Nocoupon_message = "YOU'VE REDEEMED ALL COUPONS, CHECK BACK FOR MORE IN THE FUTURE";
				}else {
					Nocoupon_message = "You’ve Redeemed All Coupons, Check Back for More in the Future";
				}
				System.out.println(Nocoupon_message);	
				isElementPresentVerification(ContentPageObjects.contentScreenFirstNme, "Content Screen - First Name");
				isElementPresentContainsText(ContentPageObjects.offeravailabilitymessage, Nocoupon_message,
						"No Coupon available message");
				isElementPresentVerification(ContentPageObjects.imgTile1, "Content Screen - Tile 1");
				isElementPresentVerification(ContentPageObjects.imgTile2, "Content Screen - Tile 2");
			}*/
			//DriverManager.getReportiumClient().stepEnd();
			
			Thread.sleep(1000);
			if (isElementPresent(CouponHomePageObjects.offermessage)) {
				if(brand.get().equalsIgnoreCase("Kodiak")) {
					String success_message = "YOU HAVE MORE OFFERS!";
					isElementPresentContainsText(CouponHomePageObjects.offermessage, success_message, "More offers message");
					isElementPresentVerification(CouponHomePageObjects.lnkbacktocoupon, "Back to Coupon Link");
					
				}else {
				String success_message = "YOU HAVE MORE OFFERS!";
				isElementPresentContainsText(CouponHomePageObjects.offermessage, success_message, "More offers message");
				isElementPresentVerification(CouponHomePageObjects.lnkbacktocoupon, "Back to Coupon Link");}
			} else {
				String Nocoupon_message = "YOU'VE REDEEMED ALL COUPONS, CHECK BACK FOR MORE IN THE FUTURE";
				//isElementPresentContainsText(CouponHomePageObjects.grizzly_NoCouponsMsg, Nocoupon_message,"No Coupon available message");
			}
		} catch (Exception e) {
			failTestScript("Failed to validate the content page: ",e);
		}
	}

	@And("^I click on back to the coupons and validate the list$")
	public void i_click_on_back_to_the_coupons_and_validate_the_list() throws Throwable {
		try {
			//DriverManager.getReportiumClient().stepStart("The user will click on back to Coupons link and the validate the user has more coupons left");
			if (isElementPresent(CouponHomePageObjects.lnkbacktocoupon)) {
				clickIfElementPresent(CouponHomePageObjects.lnkbacktocoupon, "Back to coupons link");
				waitUntilElementVisible(CouponHomePageObjects.brandImgCouponHomeHeader, 30);
				if(isElementPresent(CouponHomePageObjects.personalizedCoupon))
				isElementPresentVerification(CouponHomePageObjects.personalizedCoupon,
						"Coupon Home Page - Personalized Coupon tile");
				else
					isElementPresentVerification(CouponHomePageObjects.eitherOrCoupon,
							"Coupon Home Page - Either Or Coupon tile");
				//DriverManager.getReportiumClient().stepEnd();
			} else {
				System.out.println("The user has no coupons left");
				addStepLog("The user has no coupons left");
				//DriverManager.getReportiumClient().stepEnd();
			}
		} catch (Exception e) {
			failTestScript("Failed to click on back to the coupons and validate the list",e);
		}

	}

	@Then("^I validate the message no coupons left for the user$")
	public void i_validate_the_message_no_coupons_left_for_the_user() throws Throwable {
		try {
			//DriverManager.getReportiumClient().stepStart("Step: The user will verify the zero coupons upon coupon redemption");
			String Nocoupon_message = "You've Redeemed All Coupons, Check Back for More in the Future";
			isElementPresentContainsText(CouponHomePageObjects.Nocouponleftmsg, Nocoupon_message,
					"More offers message");
			//DriverManager.getReportiumClient().stepEnd();
			attachScreenshotForMobile();
		} catch (Exception e) {
			failTestScript("Failed to validate the no coupons left for the user",e);
		}
	}

	@When("^I click on the Not Now button$")
	public void i_click_on_the_not_now_button() throws Throwable {
		try {
			//DriverManager.getReportiumClient().stepStart("Step:The user will click on Not Now Button");
			waitUntilElementVisible(StoreListViewPageObjects.btn_NotNowAEM, 30);
			clickIfElementPresent(StoreListViewPageObjects.btn_NotNowAEM, "Store Details Page - Not Now button");
			//DriverManager.getReportiumClient().stepEnd();
			attachScreenshotForMobile();
		} catch (Exception e) {
			failTestScript("Failed to click on not now button:",e);
		}
	}

	@And("^I navigate to store details map view page$")
	public void i_navigate_to_store_details_map_view_page() throws Throwable {
		try {
			//DriverManager.getReportiumClient().stepStart("Step:The user will navigate to store details map view page");
			waitUntilElementVisible(StoreListViewPageObjects.lnk_StoreDetailsMapView, 30);
			clickIfElementPresent(StoreListViewPageObjects.lnk_StoreDetailsMapView,	"Store Details Page - Map View link");
			//DriverManager.getReportiumClient().stepEnd();
			attachScreenshotForMobile();
		} catch (Exception e) {
			failTestScript("Failed to navigate to map details view page: ",e);
		}
	}

	@And("^I navigate to store list map view page$")
	public void i_navigate_to_store_list_map_view_page() throws Throwable {
		try {
			//DriverManager.getReportiumClient().stepStart("Step: The user will navigate to the storelist map view page");
			waitUntilElementVisible(StoreListViewPageObjects.lnk_StoreListMapView, 30);
			clickIfElementPresent(StoreListViewPageObjects.lnk_StoreListMapView, "Store List Page - Map View link");
			//DriverManager.getReportiumClient().stepEnd();
		} catch (Exception e) {
			System.out.println("Exception while navigating to store list map view page: " + e.getMessage());
			failTestScript("Failed to navigate to store list map view page:  ",e);
		}
	}

	@And("^I navigate to map it page$")
	public void i_navigate_to_map_it_page() throws Throwable {
		try {
			/*
			 * int pressX = driver.manage().window().getSize().width / 2; int pressY =
			 * driver.manage().window().getSize().height / 2;
			 * 
			 * if (driver instanceof AndroidDriver) { TouchAction touchAction = new
			 * TouchAction((AndroidDriver<MobileElement>) driver);
			 * touchAction.press(PointOption.point(pressX, pressY)).release().perform(); }
			 * else { TouchAction touchAction = new TouchAction((IOSDriver<MobileElement>)
			 * driver); touchAction.press(PointOption.point(pressX,
			 * pressY)).release().perform(); }
			 */
			//DriverManager.getAppiumDriver().switchTo().frame(1);
			//waitUntilElementVisible(StoreListViewPageObjects.lnk_MapStore, 30);
			//clickIfElementPresent(StoreListViewPageObjects.lnk_MapStore, "Map Icon store link");
			driver.switchTo().frame(driver.findElement(By.tagName("iframe")));
	         WebElement element = driver.findElement(By.xpath("(//div[@role='button'])[1]"));
	          Actions actions = new Actions(driver);
	          actions.moveToElement(element).click().build().perform();
			
			clickIfElementPresent(StoreListViewPageObjects.lnk_MapIt, "Map Icon store link");
			DriverManager.getAppiumDriver().switchTo().parentFrame();
			DriverManager.getAppiumDriver().switchTo().defaultContent();
		} catch (Exception e) {
			failTestScript("Failed to navigate to map it page: ",e);
		}

	}

	@And("^I navigate to map view show directions page$")
	public void i_navigate_to_map_view_show_directions_page() throws Throwable {
		try {
			//DriverManager.getReportiumClient().stepStart("Step: The user will navigate to view show directions page");
			waitUntilElementVisible(StoreListViewPageObjects.btnShowDirections, 30);
			clickIfElementPresent(StoreListViewPageObjects.btnShowDirections,
					"Store Details Page - Show Directions button");
			//DriverManager.getReportiumClient().stepEnd();
		} catch (Exception e) {
			failTestScript("Failed to navigate to map view show direction page:",e);
		}
	}

	/*
	 * @SuppressWarnings("rawtypes")
	 * 
	 * @And("^I validate the tiles on content page for the retailer (.+) and for the brand (.+)$"
	 * ) public void
	 * i_validate_the_tiles_on_content_page_for_the_retailer_and_for_the_brand(
	 * String retailer, String brand) throws Exception { try {
	 * //DriverManager.getReportiumClient()
	 * .stepStart("Step: I validate the tiles navigate to correct page on clicking them"
	 * ); if (brand.contains("Camel")) {
	 * clickIfElementPresent(ContentPageObjects.imgTile1,
	 * "Content Screen - Tile 1"); Thread.sleep(2000); if
	 * (isElementPresent(ContentPageObjects.closeTobaccoSurveyPopUp)) {
	 * clickIfElementPresent(ContentPageObjects.closeTobaccoSurveyPopUp,
	 * "Close Tobacco Survey Pop Up"); } Thread.sleep(2000); String actualtile1Text
	 * = extractImageTextUsingScreenShot(); if
	 * (actualtile1Text.contains("ARTAFFECT UNPACKED")) {
	 * //DriverManager.getReportiumClient().
	 * reportiumAssert("The Tile 1 is navigating to the correct page", true);
	 * //DriverManager.getReportiumClient().stepEnd(); Assert.assertTrue(true,
	 * "The Tile 1 is navigating to the correct page");
	 * System.out.println("[INFO] The Tile 1 is navigating to the correct page");
	 * addStepLog("The Tile 1 is navigating to the correct page");
	 * attachScreenshotForMobile(true); } else { //DriverManager.getReportiumClient()
	 * .reportiumAssert("The Tile 1 is NOT navigating to the correct page", false);
	 * //DriverManager.getReportiumClient().stepEnd(); Assert.assertTrue(true,
	 * "The Tile 1 is NOT navigating to the correct page");
	 * System.out.println("[INFO] The Tile 1 is NOT navigating to the correct page"
	 * ); addStepLog("The Tile 1 is NOT navigating to the correct page");
	 * attachScreenshotForMobile(false); throw new
	 * CucumberException("Tile Validation:",
	 * "The Tile 1 is NOT navigating to the correct page"); } if (driver instanceof
	 * AndroidDriver) ((AndroidDriver) driver).pressKey(new
	 * KeyEvent().withKey(AndroidKey.BACK)); else driver.navigate().back();
	 * clickIfElementPresent(ContentPageObjects.imgTile2,
	 * "Content Screen - Tile 2"); Thread.sleep(5000); String tile2Text =
	 * extractImageTextUsingScreenShot(); String expectedTile2Text = null; if
	 * (retailer.contains("Murphy")) { expectedTile2Text =
	 * "Looking for Deals, Rewards or a chance to Rev Up?"; } else {
	 * expectedTile2Text = "Transforming communities through art."; } if
	 * (tile2Text.contains(expectedTile2Text)) { //DriverManager.getReportiumClient().
	 * reportiumAssert("The Tile 1 is navigating to the correct page", true);
	 * //DriverManager.getReportiumClient().stepEnd(); Assert.assertTrue(true,
	 * "The Tile 2 is navigating to the correct page");
	 * System.out.println("[INFO] The Tile 2 is navigating to the correct page");
	 * addStepLog("The Tile 2 is navigating to the correct page");
	 * attachScreenshotForMobile(true); } else { //DriverManager.getReportiumClient()
	 * .reportiumAssert("The Tile 2 is NOT navigating to the correct page", false);
	 * //DriverManager.getReportiumClient().stepEnd(); Assert.assertTrue(true,
	 * "The Tile 2 is NOT navigating to the correct page");
	 * System.out.println("[INFO] The Tile 2 is NOT navigating to the correct page"
	 * ); addStepLog("The Tile 2 is NOT navigating to the correct page");
	 * attachScreenshotForMobile(false); throw new
	 * CucumberException("Tile Validation:",
	 * "The Tile 2 is NOT navigating to the correct page"); } } else if
	 * (brand.contains("Newport")) {
	 * clickIfElementPresent(ContentPageObjects.imgTile1,
	 * "Content Screen - Tile 1"); if
	 * (isElementPresent(ContentPageObjects.closeTobaccoSurveyPopUp)) {
	 * clickIfElementPresent(ContentPageObjects.closeTobaccoSurveyPopUp,
	 * "Close Tobacco Survey Pop Up"); } Thread.sleep(2000); String tile1Text =
	 * extractImageTextUsingScreenShot(); if (tile1Text.contains("Lounge")) {
	 * //DriverManager.getReportiumClient().
	 * reportiumAssert("The Tile 1 is navigating to the correct page", true);
	 * //DriverManager.getReportiumClient().stepEnd(); Assert.assertTrue(true,
	 * "The Tile 1 is navigating to the correct page");
	 * System.out.println("[INFO] The Tile 1 is navigating to the correct page");
	 * addStepLog("The Tile 1 is navigating to the correct page");
	 * attachScreenshotForMobile(true); } else { //DriverManager.getReportiumClient()
	 * .reportiumAssert("The Tile 1 is NOT navigating to the correct page", false);
	 * //DriverManager.getReportiumClient().stepEnd(); Assert.assertTrue(true,
	 * "The Tile 1 is NOT navigating to the correct page");
	 * System.out.println("[INFO] The Tile 1 is NOT navigating to the correct page"
	 * ); addStepLog("The Tile 1 is NOT navigating to the correct page");
	 * attachScreenshotForMobile(false); throw new
	 * CucumberException("Tile Validation:",
	 * "The Tile 1 is NOT navigating to the correct page"); } if (driver instanceof
	 * AndroidDriver) ((AndroidDriver) driver).pressKey(new
	 * KeyEvent().withKey(AndroidKey.BACK)); else driver.navigate().back();
	 * clickIfElementPresent(ContentPageObjects.imgTile2,
	 * "Content Screen - Tile 2"); Thread.sleep(2000); String tile2Text =
	 * extractImageTextUsingScreenShot(); String expectedTile2Text = null; if
	 * (retailer.contains("Murphy")) { expectedTile2Text =
	 * "Looking for Deals, Rewards or a chance to Rev Up?"; } else {
	 * expectedTile2Text = "LET'S PLAY"; } if
	 * (tile2Text.contains(expectedTile2Text)) { //DriverManager.getReportiumClient().
	 * reportiumAssert("The Tile 1 is navigating to the correct page", true);
	 * //DriverManager.getReportiumClient().stepEnd(); Assert.assertTrue(true,
	 * "The Tile 2 is navigating to the correct page");
	 * System.out.println("[INFO] The Tile 2 is navigating to the correct page");
	 * addStepLog("The Tile 2 is navigating to the correct page");
	 * attachScreenshotForMobile(true); } else { //DriverManager.getReportiumClient()
	 * .reportiumAssert("The Tile 2 is NOT navigating to the correct page", false);
	 * //DriverManager.getReportiumClient().stepEnd(); Assert.assertTrue(true,
	 * "The Tile 2 is NOT navigating to the correct page");
	 * System.out.println("[INFO] The Tile 2 is NOT navigating to the correct page"
	 * ); addStepLog("The Tile 2 is NOT navigating to the correct page");
	 * attachScreenshotForMobile(false); throw new
	 * CucumberException("Tile Validation:",
	 * "The Tile 2 is NOT navigating to the correct page"); } } else if
	 * (brand.contains("Grizzly")) {
	 * clickIfElementPresent(ContentPageObjects.imgTile1,
	 * "Content Screen - Tile 1"); Thread.sleep(2000); if
	 * (isElementPresent(ContentPageObjects.closeTobaccoSurveyPopUp)) {
	 * clickIfElementPresent(ContentPageObjects.closeTobaccoSurveyPopUp,
	 * "Close Tobacco Survey Pop Up"); } Thread.sleep(2000); String tile1Text =
	 * extractImageTextUsingScreenShot(); if (tile1Text.contains("NO SHORTCUTS")) {
	 * //DriverManager.getReportiumClient().
	 * reportiumAssert("The Tile 1 is navigating to the correct page", true);
	 * //DriverManager.getReportiumClient().stepEnd(); Assert.assertTrue(true,
	 * "The Tile 1 is navigating to the correct page");
	 * System.out.println("[INFO] The Tile 1 is navigating to the correct page");
	 * addStepLog("The Tile 1 is navigating to the correct page");
	 * attachScreenshotForMobile(true); } else { //DriverManager.getReportiumClient()
	 * .reportiumAssert("The Tile 1 is NOT navigating to the correct page", false);
	 * //DriverManager.getReportiumClient().stepEnd(); Assert.assertTrue(true,
	 * "The Tile 1 is NOT navigating to the correct page");
	 * System.out.println("[INFO] The Tile 1 is NOT navigating to the correct page"
	 * ); addStepLog("The Tile 1 is NOT navigating to the correct page");
	 * attachScreenshotForMobile(false); throw new
	 * CucumberException("Tile Validation:",
	 * "The Tile 2 is NOT navigating to the correct page"); } if (driver instanceof
	 * AndroidDriver) ((AndroidDriver) driver).pressKey(new
	 * KeyEvent().withKey(AndroidKey.BACK)); else driver.navigate().back();
	 * clickIfElementPresent(ContentPageObjects.imgTile2,
	 * "Content Screen - Tile 2"); Thread.sleep(5000); String tile2Text =
	 * extractImageTextUsingScreenShot(); String expectedTile2Text = null; if
	 * (retailer.contains("Murphy")) { expectedTile2Text =
	 * "Looking for Deals, Rewards or a chance to Rev Up?"; } else {
	 * expectedTile2Text = "Start Quiz"; } if
	 * (tile2Text.contains(expectedTile2Text)) { //DriverManager.getReportiumClient().
	 * reportiumAssert("The Tile 1 is navigating to the correct page", true);
	 * //DriverManager.getReportiumClient().stepEnd(); Assert.assertTrue(true,
	 * "The Tile 2 is navigating to the correct page");
	 * System.out.println("[INFO] The Tile 2 is navigating to the correct page");
	 * addStepLog("The Tile 2 is navigating to the correct page");
	 * attachScreenshotForMobile(true); } else { //DriverManager.getReportiumClient()
	 * .reportiumAssert("The Tile 2 is NOT navigating to the correct page", false);
	 * //DriverManager.getReportiumClient().stepEnd(); Assert.assertTrue(true,
	 * "The Tile 2 is NOT navigating to the correct page");
	 * System.out.println("[INFO] The Tile 2 is NOT navigating to the correct page"
	 * ); addStepLog("The Tile 2 is NOT navigating to the correct page");
	 * attachScreenshotForMobile(false); throw new
	 * CucumberException("Tile Validation:",
	 * "The Tile 2 is NOT navigating to the correct page");
	 * 
	 * } } if (driver instanceof AndroidDriver) ((AndroidDriver)
	 * driver).pressKey(new KeyEvent().withKey(AndroidKey.BACK)); else
	 * driver.navigate().back(); //DriverManager.getReportiumClient().stepEnd(); }
	 * catch (Exception e) {
	 * failTestScript("The Tile is NOT navigating to the correct page:", e); } }
	 */
	
	@And("^I validate the tiles on content page for the retailer (.+) and for the brand (.+) with expected Tile1 url (.+) and excected Tile2 url (.+)$")
    public void i_validate_the_tiles_on_content_page_for_the_retailer_and_for_the_brand_with_expected_tile1_url_and_excected_tile2_url(String retailer, String brand, String expectedtile1Url, String expectedtile2Url) {
		try {
			waitUntilPageReadyStateComplete(20);
			clickIfElementPresent(ContentPageObjects.redeemTile1, "Clicked on redeem- Tile 1");
			Thread.sleep(5000);
			
			ArrayList<String> w=new ArrayList<String>(driver.getWindowHandles());
			driver.switchTo().window(w.get(1));
			
			Thread.sleep(2000);

			System.out.println(driver.getCurrentUrl() +" "+ expectedtile1Url);
			String actualtile1Url =driver.getCurrentUrl();
			
			compareStringsContains(actualtile1Url, expectedtile1Url);
	
			if (driver instanceof AndroidDriver)
				((AndroidDriver) driver).pressKey(new KeyEvent().withKey(AndroidKey.BACK));
			else
				driver.navigate().back();
				
			Thread.sleep(2000);
			driver.switchTo().window(w.get(0));
			
			Thread.sleep(2000);
			clickIfElementPresent(ContentPageObjects.redeemTile2, "Clicked on redeem- Tile 2");
			Thread.sleep(5000);
			if(!(LoginPageStepDefs.getBrand().equalsIgnoreCase("Camel")) || (LoginPageStepDefs.getBrand().equalsIgnoreCase("Cougar") )|| (LoginPageStepDefs.getBrand().equalsIgnoreCase("Camelsnus"))){
			ArrayList<String> el=new ArrayList<String>(driver.getWindowHandles());
			driver.switchTo().window(el.get(1));
			
			Thread.sleep(4000);
			String actualtile2Url =driver.getCurrentUrl();
			compareStringsContains(actualtile2Url, expectedtile2Url);
			if (driver instanceof AndroidDriver)
				((AndroidDriver) driver).pressKey(new KeyEvent().withKey(AndroidKey.BACK));
			else
				driver.navigate().back();
						
			driver.switchTo().window(w.get(0));
			
			Thread.sleep(2000);
			}
		} catch (Exception e) {
			failTestScript("The Tile is NOT navigating to the correct page:", e);
		}
    }
	

	@And("^I validate the tiles on content page for the retailer (.+) and for the brand (.+) with expected Tile1 text (.+) and excected Tile2 text (.+)$")
    public void i_validate_the_tiles_on_content_page_for_the_retailer_and_for_the_brand_with_expected_tile1_text_and_excected_tile2_text(String retailer, String brand, String expectedtile1text, String expectedtile2text) {
		try {
			//DriverManager.getReportiumClient().stepStart("Step: I validate the tiles navigate to correct page on clicking them");
			
			String murphymsg = null;
			clickIfElementPresent(ContentPageObjects.imgTile1, "Content Screen - Tile 1");
			Thread.sleep(5000);
			waitUntilPageReadyStateComplete(30);
			if (isElementPresent(ContentPageObjects.naspopup) && brand.contains("NAS")) {
				clickIfElementPresent(ContentPageObjects.naspopup, "Close Tobacco Survey Pop Up");
			}
			if (isElementPresent(ContentPageObjects.closeTobaccoSurveyPopUp)) {
				clickIfElementPresent(ContentPageObjects.closeTobaccoSurveyPopUp, "Close Tobacco Survey Pop Up");
			}
			Thread.sleep(2000);
			if (driver instanceof AndroidDriver) {
				switchToContext(driver, "NATIVE_APP");
				if (isElementPresent(LocationServices.androidLocationServicesPermissionAllow))
				clickIfElementPresent(LocationServices.androidLocationServicesPermissionAllow,
						"Location services Permission - Allow Button");
				switchToContext(driver, "NATIVE_APP");
				//DriverManager.getReportiumClient().stepEnd();
			}
			waitFor(1000);
			if (isElementPresent(ContentPageObjects.naspopup) && brand.contains("NAS")) {
				clickIfElementPresent(ContentPageObjects.naspopup, "Close Tobacco Survey Pop Up");
			}
			String actualtile1Text = extractImageTextUsingScreenShot();
			actualtile1Text=actualtile1Text.replace("\n", " ");
		    compareStringsContains(actualtile1Text, expectedtile1text);
//			if (actualtile1Text.contains(expectedtile1text)) {
//				//DriverManager.getReportiumClient().reportiumAssert("The Tile 1 is navigating to the correct page",
//						true);
//				//DriverManager.getReportiumClient().stepEnd();
//				Assert.assertTrue(true, "The Tile 1 is navigating to the correct page");
//				System.out.println("[INFO] The Tile 1 is navigating to the correct page");
//				addStepLog("The Tile 1 is navigating to the correct page");
//				attachScreenshotForMobile(true);
//			} else {
//				//DriverManager.getReportiumClient()
//						.reportiumAssert("The Tile 1 is NOT navigating to the correct page", false);
//				//DriverManager.getReportiumClient().stepEnd();
//				Assert.assertTrue(true, "The Tile 1 is NOT navigating to the correct page");
//				System.out.println("[INFO] The Tile 1 is NOT navigating to the correct page");
//				addStepLog("The Tile 1 is NOT navigating to the correct page");
//				attachScreenshotForMobile(false);
//				throw new CucumberException("Tile Validation:", "The Tile 1 is NOT navigating to the correct page");
//			}
			if (driver instanceof AndroidDriver)
				((AndroidDriver) driver).pressKey(new KeyEvent().withKey(AndroidKey.BACK));
			else
				driver.navigate().back();
			if(retailer.contains("Murphy"))
			{
				if(brand.contains("NAS") || brand.contains("Cougar") || brand.contains("Kodiak") || brand.contains("Pallmall")  || brand.contains("Luckystrike") || brand.contains("Newport")|| brand.contains("Grizzly"))
					murphymsg = "Save more with Murphy Drive Rewards";
				else
					murphymsg = "SAVE MORE WITH MURPHY DRIVE REWARDS";
				//isElementPresentContainsText(ContentPageObjects.murphytilemsg, 	murphymsg,
					//	"Verifying Murphy Tile");
			}
			else
			{
			clickIfElementPresent(ContentPageObjects.imgTile2, "Content Screen - Tile 2");
			Thread.sleep(5000);
			waitUntilPageReadyStateComplete(30);
			String tile2Text = extractImageTextUsingScreenShot();
			tile2Text=tile2Text.replace("\n", " ");
		    compareStringsContains(tile2Text, expectedtile2text);
//			if (tile2Text.contains(expectedtile2text)) {
//				//DriverManager.getReportiumClient().reportiumAssert("The Tile 1 is navigating to the correct page",
//						true);
//				//DriverManager.getReportiumClient().stepEnd();
//				Assert.assertTrue(true, "The Tile 2 is navigating to the correct page");
//				System.out.println("[INFO] The Tile 2 is navigating to the correct page");
//				addStepLog("The Tile 2 is navigating to the correct page");
//				attachScreenshotForMobile(true);
//			} else {
//				//DriverManager.getReportiumClient()
//						.reportiumAssert("The Tile 2 is NOT navigating to the correct page", false);
//				//DriverManager.getReportiumClient().stepEnd();
//				Assert.assertTrue(true, "The Tile 2 is NOT navigating to the correct page");
//				System.out.println("[INFO] The Tile 2 is NOT navigating to the correct page");
//				addStepLog("The Tile 2 is NOT navigating to the correct page");
//				attachScreenshotForMobile(false);
//				throw new CucumberException("Tile Validation:", "The Tile 2 is NOT navigating to the correct page");
//			}
			}
			if (driver instanceof AndroidDriver)
				((AndroidDriver) driver).pressKey(new KeyEvent().withKey(AndroidKey.BACK));
			else
				driver.navigate().back();
			//DriverManager.getReportiumClient().stepEnd();
		} catch (Exception e) {
			failTestScript("The Tile is NOT navigating to the correct page:", e);
		}
    }
	
	 


}
