package com.rai.steps;

import static org.testng.Assert.fail;

import java.util.HashMap;
import java.util.Map;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.openqa.selenium.By;
import org.openqa.selenium.JavascriptExecutor;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;

import com.rai.framework.DriverManager;
import com.rai.pages.CouponHomePageObjects;
import com.rai.pages.StoreListViewPageObjects;
import io.appium.java_client.AppiumDriver;
import io.appium.java_client.android.AndroidDriver;
import io.appium.java_client.ios.IOSDriver;
import io.cucumber.java.en.And;
import io.cucumber.java.en.Then;

public class SearchByZipStepDefs extends MasterSteps {

	static Logger log = LogManager.getLogger(SearchByZipStepDefs.class);
	AppiumDriver driver = DriverManager.getAppiumDriver();	

	 @And("^I do a Search By zip with valid zip code (.+)$")
	  public void i_do_a_search_by_zip_with_valid_zip_code(String zipcode) {
		try {
			WebElement element = (WebElement) DriverManager.getAppiumDriver()
					.findElement(StoreListViewPageObjects.txt_Zipcode);
			if (element.isDisplayed())
				element.clear();
//			if(LoginPageStepDefs.getBrand().contains("NAS"))
//				zipcodeVisualNAS(StoreListViewPageObjects.txt_Zipcode, zipcode , "Store List Page - Zipcode textbox");
//			else
//			{
				//zipcodeVisual(StoreListViewPageObjects.txt_Zipcode, zipcode ,  "Store List Page - Zipcode textbox");
				driver.findElement(By.xpath("//input[contains(@id,'_txtzipcode')]")).click();
				driver.findElement(StoreListViewPageObjects.txt_Zipcode).sendKeys(zipcode);
//			}
				
		} catch (Exception e) {
			failTestScript("Exception while searching with valid zip code:",e);
		}
	}
	
	@And("I do a Search By zip with invalid zip code")
	public void i_do_a_Search_By_zip_with_invalid_zip_code() {
		try {
			//DriverManager.getReportiumClient().stepStart("Step:The user will search with a invalid Zip code");
//			waitUntilElementVisible(StoreListViewPageObjects.txt_Zipcode, 30);
			Thread.sleep(3000);
			if(LoginPageStepDefs.getBrand().contains("NAS"))
				driver.findElement(By.xpath("//input[contains(@id,'_txtzipcode')]")).sendKeys("99999");
//				zipcodeVisualNAS(StoreListViewPageObjects.txt_Zipcode, "99999", "Store List Page - Zipcode textbox");
			else
			{
				Thread.sleep(1000);
				waitUntilElementVisible(StoreListViewPageObjects.txt_Zipcode, 30);
				driver.findElement(By.xpath("//input[contains(@id,'_txtzipcode')]")).click();
				driver.findElement(By.xpath("//input[contains(@id,'_txtzipcode')]")).sendKeys("99999");
			}
				//zipcodeVisual(StoreListViewPageObjects.txt_Zipcode, "99999",  "Store List Page - Zipcode textbox");
			//DriverManager.getReportiumClient().stepEnd();
		} catch (Exception e) {
			failTestScript("Exception while searching with invalid zip code:",e);
		}
	}

	@And("I select a store from store list page")
	public void i_select_a_store_from_store_list_page() {
		try {
			//DriverManager.getReportiumClient().stepStart("Step: The user will select a store from the store list");
			//waitUntilElementVisible(StoreListViewPageObjects.lnk_Store, 30);
			Thread.sleep(5000);
			clickIfElementPresent(StoreListViewPageObjects.lnk_Store, "Store List Page - Store link");
			waitUntilElementVisible(StoreListViewPageObjects.btn_RedeemNowAEM, 30);
//			driver.findElement(StoreListViewPageObjects.btn_RedeemNowAEM).click();
			//DriverManager.getReportiumClient().stepEnd();
		} catch (Exception e) {
			failTestScript("Exception while selecting store list page:",e);
		}
	}
	
	@And("^I navigate to mapview page$")
    public void i_navigate_to_mapview_page()  {
		try {
			//DriverManager.getReportiumClient().stepStart("Step:The user will navigate to Mapview page");
			waitUntilElementVisible(CouponHomePageObjects.btnmapview, 20);
			clickIfElementPresent(CouponHomePageObjects.btnmapview, "MapDetails link");
			waitFor(2000);
			//DriverManager.getReportiumClient().stepEnd();
		} catch (Exception e) {
			failTestScript("Exception while navigating to mapview page: ",e);
		}
    }

	@And("I select a store from map view")
	public void i_select_a_store_from_map_view() {
		try {
			//DriverManager.getAppiumDriver().switchTo().frame(1);
			Thread.sleep(4000);
			driver.switchTo().frame(driver.findElement(By.tagName("iframe")));
			Thread.sleep(3000);
	         WebElement element = driver.findElement(By.xpath("(//div[@role='button'])[1]"));
	         JavascriptExecutor executor = (JavascriptExecutor)driver;
	         executor.executeScript("arguments[0].click();", element);
			//waitUntilElementVisible(StoreListViewPageObjects.lnk_MapStore, 30);
			//clickIfElementPresent(StoreListViewPageObjects.lnk_MapStore, "Map Icon store link");
	         Thread.sleep(5000);
			clickIfElementPresent(StoreListViewPageObjects.lnk_MapStoreLink, "Leftarrow link");
			DriverManager.getAppiumDriver().switchTo().parentFrame();
			DriverManager.getAppiumDriver().switchTo().defaultContent();
		} catch (Exception e) {
			failTestScript("Exception while selecting a store from map view:" ,e);
		}
		
	}
	
	@And("^I validate the user is on Redeem now page$")
    public void i_validate_the_user_is_on_redeem_now_page()  {
		try {
			//DriverManager.getReportiumClient().stepStart("Step: The user will validate the user is on Redeem now page");
			waitUntilElementVisible(StoreListViewPageObjects.btn_RedeemNow, 20);
			isElementPresentVerification(StoreListViewPageObjects.btn_RedeemNow, "Button Redeem Now");
			//DriverManager.getReportiumClient().stepEnd();
		} catch (Exception e) {
			failTestScript("Exception while validating the user is on redeem now page:" ,e);
		}
    }

	@And("I click on the Redeem Now button")
	public void i_click_on_redeem_now_button() {
		try {
			//DriverManager.getReportiumClient().stepStart("Step: The user will click on Redeem now button");
			waitUntilElementVisible(StoreListViewPageObjects.btn_RedeemNowAEM, 30);
			clickIfElementPresent(StoreListViewPageObjects.btn_RedeemNowAEM, "Store Details Page - Redeem Now button");
			Thread.sleep(10000);
			//DriverManager.getReportiumClient().stepEnd();
		} catch (Exception e) {
			failTestScript("Exception while clicking on redeem now button: " ,e);
		}
	}
	
	@Then("I validate the error message for invalid zipcode")
	public void i_validate_the_error_message_for_invalid_zipcode() {
		try {
			//DriverManager.getReportiumClient().stepStart("Step:The user will validate the error Message for invalid Zipcode");
			String errmsg_InvalidZip = "We are sorry but there are no participating stores in your area at this time. Please check back in the future.";
//			waitUntilElementVisible(StoreListViewPageObjects.errormsg_PopupStoreList, 30);
			Thread.sleep(3000);
			isElementPresentContainsText(StoreListViewPageObjects.errormsg_PopupStoreList, errmsg_InvalidZip,"Store List Page - Error popup message");
			clickIfElementPresent(StoreListViewPageObjects.btn_Close, "Store List Page - Close popup button");
			//DriverManager.getReportiumClient().stepEnd();
		} catch (Exception e) {
			failTestScript("Exception while validating the error message for invalid zipcode: " ,e);
		}
	}
	
	@Then("I validate the error message on coupon redemption")
	public void i_validate_the_error_message_on_coupon_redemption() {
		try {
			//DriverManager.getReportiumClient().stepStart("The user will validate the error message on Coupon redemption");
			String errmsg_RedeemNow = "You must be in the store to redeem this coupon offer.";
			clickIfElementPresent(StoreListViewPageObjects.btn_RedeemNow, "Store details Page - Redeem Now button");
			Thread.sleep(4000);
			waitUntilElementVisible(StoreListViewPageObjects.errormsg_Popup, 30);
			isElementPresentContainsText(StoreListViewPageObjects.errormsg_Popup, errmsg_RedeemNow,	"Store details Page - Error popup message");
			waitUntilElementVisible(StoreListViewPageObjects.btn_Close, 30);
			clickIfElementPresent(StoreListViewPageObjects.btn_Close, "Store details Page - Close popup button");
		} catch (Exception e) {
			failTestScript("Failed to validate the error message on coupon redemption" ,e);
		}
	}

	@And("^I mark the store as favorite$")
    public void i_mark_the_store_as_favorite()  {
		try {
			//DriverManager.getReportiumClient().stepStart("The user will mark the store as favorite store");
			waitUntilElementVisible(StoreListViewPageObjects.chckbx_favstore, 20);
			clickIfElementPresent(StoreListViewPageObjects.chckbx_favstore, "favorite Store Check box");
			//DriverManager.getReportiumClient().stepEnd();
		} catch (Exception e) {
			failTestScript("Exception while marking the store as favorite:" ,e);
		}
    }
	
	@And("^I navigate to Store List and validate the favoritestore$")
    public void i_navigate_to_store_list_and_validate_the_favoritestore()  {
		try {
			//DriverManager.getReportiumClient().stepStart("Step: The user will navigate to the store list and validate the gold star");
			waitUntilElementVisible(StoreListViewPageObjects.lnk_storeDownarrow, 20);
			clickIfElementPresent(StoreListViewPageObjects.lnk_storeDownarrow, "Downarrow in store list");
			isElementPresentVerification(StoreListViewPageObjects.GoldStar, "Gold star Favorite");
			addStepLog("Step: The user validated the store as a favorite");
			clickIfElementPresent(StoreListViewPageObjects.lnk_Store, "Store List Page - Store link");
			clickIfElementPresent(StoreListViewPageObjects.chckbx_favstore, "favorite Store Check box");
			addStepLog("The user will unmark the store from favoritestore");
			clickIfElementPresent(StoreListViewPageObjects.lnk_storeDownarrow, "Downarrow in store list");
			isElementPresentVerification(StoreListViewPageObjects.GreyStar, "Grey star image");
			//DriverManager.getReportiumClient().stepEnd();
		}catch (Exception e) {
			failTestScript("Exception while validating the favoritestore:" ,e);
		}
    }

	public void zipcodeVisual(By ele, String zip, String objName) {
		//DriverManager.getReportiumClient()
//				.stepStart("Validate user is able to enter the text in the text box:" + objName);
		WebElement element = (WebElement) DriverManager.getAppiumDriver().findElement(ele);
		if (element.isDisplayed()) {
			highLightElement(DriverManager.getAppiumDriver().findElement(ele), "LawnGreen");
			element.clear();
			Map<String, Object> params = new HashMap<>();
			if (driver instanceof IOSDriver)
				((IOSDriver) driver).context("VISUAL");
			else
				((AndroidDriver) driver).context("VISUAL");
			try {
				params.clear();
				params.put("label", "SEARCH BY ZIP");
				params.put("threshold", 50);
				//params.put("operation", "single");
				System.out.println("Zip : " + zip);
				params.put("text", zip);
				((RemoteWebDriver) driver).executeScript("mobile:edit-text:set", params);
			} catch (Exception e) {
				System.out.println("[INFO] Zipcode is successfully entered");
			}
			if (driver instanceof IOSDriver)
				((IOSDriver) driver).context("WEBVIEW_1");
			else
				((AndroidDriver) driver).context("WEBVIEW_1");
			//DriverManager.getReportiumClient().reportiumAssert("The text box- " + objName + " is present and the text-" + zip + " is successfully entered", true);
			//DriverManager.getReportiumClient().stepEnd();
			Assert.assertTrue(true,	"The text box- " + objName + " is present and the text-" + zip + " is successfully entered");
			addStepLog("The text box- " + objName + " is present and the text-" + zip + " is successfully entered");
		} else {
			//DriverManager.getReportiumClient().reportiumAssert(	"The text box- " + objName + " is NOT present and the text-" + zip + " is NOT entered", false);
			//DriverManager.getReportiumClient().stepEnd();
			Assert.assertTrue(false,"The text box- " + objName + " is present and the text-" + zip + " is successfully entered");
			attachScreenshotForMobile(false);
			fail("The text box- " + objName + " is present and the text-" + zip + " is successfully entered");
		}
	}	
	public void zipcodeVisualNAS(By ele, String zip, String objName) {
		//DriverManager.getReportiumClient()
//				.stepStart("Validate user is able to enter the text in the text box:" + objName);
		System.out.println("In NAS zipcodeVisual method");
		WebElement element = (WebElement) DriverManager.getAppiumDriver().findElement(ele);
		if (element.isDisplayed()) {
			highLightElement(DriverManager.getAppiumDriver().findElement(ele), "LawnGreen");
			element.clear();
			Map<String, Object> params = new HashMap<>();
			if (driver instanceof IOSDriver)
				((IOSDriver) driver).context("VISUAL");
			else
				((AndroidDriver) driver).context("VISUAL");
			try {
				params.clear();
				params.put("label", "SPIRIT");
				params.put("threshold", 50);
				params.put("label.direction", "above");
				params.put("label.offset", "5%");
				System.out.println("Zip : " + zip);
				params.put("text", zip);
				((RemoteWebDriver) driver).executeScript("mobile:edit-text:set", params);
			} catch (Exception e) {
				System.out.println("[INFO] Zipcode is successfully entered");
			}
			if (driver instanceof IOSDriver)
				((IOSDriver) driver).context("WEBVIEW_1");
			else
				((AndroidDriver) driver).context("WEBVIEW_1");
			//DriverManager.getReportiumClient().reportiumAssert("The text box- " + objName + " is present and the text-" + zip + " is successfully entered", true);
			//DriverManager.getReportiumClient().stepEnd();
			Assert.assertTrue(true,	"The text box- " + objName + " is present and the text-" + zip + " is successfully entered");
			addStepLog("The text box- " + objName + " is present and the text-" + zip + " is successfully entered");
		} else {
			//DriverManager.getReportiumClient().reportiumAssert(	"The text box- " + objName + " is NOT present and the text-" + zip + " is NOT entered", false);
			//DriverManager.getReportiumClient().stepEnd();
			Assert.assertTrue(false,"The text box- " + objName + " is present and the text-" + zip + " is successfully entered");
			attachScreenshotForMobile(false);
			fail("The text box- " + objName + " is present and the text-" + zip + " is successfully entered");
		}
	}
}


