<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE suite SYSTEM "https://testng.org/testng-1.0.dtd">
<suite name="Run Cucumber TestNG" parallel="tests" data-provider-thread-count="2"	verbose="5">

	<listeners>
		<listener class-name="com.rai.framework.TestNGListener" />
	</listeners>

	<!-- Possible Parameters and Values -->
	<!-- ExecutionMode = API,LOCAL,GRID,MO<PERSON>LE,SAUCEMOBILE,PERFECTODESKTOP -->
	<!-- ToolName= APPIUM,REMOTEDRIVER -->
	<!-- MobileExecutionPlatform = ANDROID, IOS, WEB_ANDROID, WEB_IOS -->
	<!-- BrowserName = CHROME,FIREFOX,INTERNET_EXPLORER,EDGE -->
	<!-- BrowserVersion= -->
	<!-- DeviceName = "Please give the respective deviceName/UDID" OR ManufacturerName 
		= Apple etc., ModelName = iPhone 7etc., -->
	<!-- MobileOsVersion ="" -->
	<!-- PlatformVersion ="" -->
		
		<suite-files>
			<suite-file path="TestNG_MobileRBDS_Camel_Prod.xml"/>
			<suite-file path="TestNG_MobileRBDS_Camelsnus_Prod.xml"/>
			<suite-file path="TestNG_MobileRBDS_Cougar_Prod.xml"/>
			<suite-file path="TestNG_MobileRBDS_Grizzly_Prod.xml"/>
			<suite-file path="TestNG_MobileRBDS_Kodiak_Prod.xml"/>
			<suite-file path="TestNG_MobileRBDS_LS_Prod.xml"/>
			<suite-file path="TestNG_MobileRBDS_NAS_Prod.xml"/>
			<suite-file path="TestNG_MobileRBDS_Newport_Prod.xml"/>
			<suite-file path="TestNG_MobileRBDS_Pallmall_Prod.xml"/>
			<suite-file path="TestNG_MobileRBDS_Velo_Prod.xml"/>
			<suite-file path="TestNG_MobileRBDS_Vuse_Prod.xml"/>
		</suite-files>
</suite>